import type { UploadRawFile } from 'element-plus/es/components/upload/src/upload';

export type UploadFile = {
  file: UploadRawFile;
  dirTag?: string;
};

export type UploadResult = {
  url: string;
  filename: string;
  eTag: string;
  objectName: string;
  dirTag: string;
  contextType: string;
  size: number;
  fileId: number;
};

// 新的服务器响应格式
export type NewUploadResult = {
  id: string;
  name: string;
  url: string;
};

export type IUploadResult = UploadResult | null;
export type INewUploadResult = NewUploadResult | null;

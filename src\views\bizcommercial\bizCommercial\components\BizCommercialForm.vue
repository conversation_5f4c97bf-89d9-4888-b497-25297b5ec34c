<template>
  <el-dialog
    v-model="visible"
    :title="`${paramsProps.title}`"
    :destroy-on-close="true"
    width="580px"
    draggable
  >
    <el-form
      ref="ruleFormRef"
      label-width="140px"
      label-suffix=" :"
      :rules="rules"
      :model="paramsProps.row"
      @submit.enter.prevent="handleSubmit"
    >
      <el-form-item label="文件" prop="fileId">
        <UploadFiles
          v-model:file-list="fileList"
          :limit="1"
          :file-size="5"
          width="300px"
          height="120px"
          @change="fileChange"
        >
          <template #tip> 文件大小不能超过 5M </template>
        </UploadFiles>
      </el-form-item>
      <el-form-item label="显示顺序" prop="sortNum">
        <el-input-number
          v-model="paramsProps.row.sortNum" :precision="0" :min="1" :max="999999" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="paramsProps.row.remark"
          placeholder="请填写备注"
          clearable
        ></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false"> 取消</el-button>
      <el-button type="primary" @click="handleSubmit"> 确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { type ElForm, ElMessage, type UploadUserFile } from 'element-plus'
import UploadFiles from '@/components/Upload/file.vue'
import type { INewUploadResult } from '@/api/types/system/upload'

defineOptions({
    name: 'BizCommercialForm'
})

const rules = reactive({
  fileId: [{ required: true, message: '请上传文件' }],
})

const visible = ref(false)
const paramsProps = ref<View.DefaultParams>({
  title: '',
  row: {},
  api: undefined,
  getTableList: undefined
})

const fileList = ref<UploadUserFile[]>([])

// 接收父组件传过来的参数
const acceptParams = (params: View.DefaultParams) => {
  paramsProps.value = params
  visible.value = true

  // 初始化文件列表（编辑模式下暂不显示已有文件，只允许重新上传）
  fileList.value = []
}

// 文件上传变化处理
const fileChange = (file: INewUploadResult) => {
  if (file) {
    paramsProps.value.row.fileId = parseInt(file.id)
  } else {
    paramsProps.value.row.fileId = null
  }
}

// 提交数据（新增/编辑）
const ruleFormRef = ref<InstanceType<typeof ElForm>>()
const handleSubmit = () => {
  ruleFormRef.value!.validate(async (valid) => {
    if (!valid) return
    try {
      await paramsProps.value.api!(paramsProps.value.row)
      ElMessage.success({ message: `${paramsProps.value.title}成功！` })
      paramsProps.value.getTableList!()
      visible.value = false
    } catch (error) {
      console.log(error)
    }
  })
}

defineExpose({
  acceptParams
})
</script>

<style scoped lang="scss"></style>